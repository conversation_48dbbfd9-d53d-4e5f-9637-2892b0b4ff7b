-- Create all sequences first
CREATE SEQUENCE IF NOT EXISTS tenant_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS location_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS floor_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS power_grid_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS csms_charging_station_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS connector_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_transaction_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_transaction_event_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_variable_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_variable_value_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS report_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS meter_value_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS csms_user_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS csms_user_role_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS session_owner_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS session_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS transaction_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS authorization_code_request_data_id_seq START WITH 1 INCREMENT BY 1;

-- Create tenant table
CREATE TABLE IF NOT EXISTS tenant (
    id BIGINT PRIMARY KEY DEFAULT nextval('tenant_id_seq'),
    name VARCHAR(255) UNIQUE,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create location table
CREATE TABLE IF NOT EXISTS location (
    id BIGINT PRIMARY KEY DEFAULT nextval('location_id_seq'),
    name VARCHAR(255),
    tenant_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create floor table
CREATE TABLE IF NOT EXISTS floor (
    id BIGINT PRIMARY KEY DEFAULT nextval('floor_id_seq'),
    level INTEGER NOT NULL,
    name VARCHAR(255),
    layout VARCHAR(50),
    row_count INTEGER NOT NULL,
    column_count INTEGER NOT NULL,
    location_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create power_grid table
CREATE TABLE IF NOT EXISTS power_grid (
    id BIGINT PRIMARY KEY DEFAULT nextval('power_grid_id_seq'),
    name VARCHAR(255),
    location_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Drop and recreate charging_station table with all columns
DROP TABLE IF EXISTS charging_station CASCADE;
CREATE TABLE charging_station (
    id BIGINT PRIMARY KEY DEFAULT nextval('csms_charging_station_id_seq'),
    name VARCHAR(255),
    url VARCHAR(255),
    status VARCHAR(50),
    availability_status VARCHAR(50),
    last_status_update TIMESTAMP WITH TIME ZONE,
    error_code VARCHAR(255),
    error_info VARCHAR(255),
    vendor_implementation_id VARCHAR(255),
    vendor_error_code VARCHAR(255),
    charge_point_serial_number VARCHAR(255),
    charge_point_vendor VARCHAR(255),
    meter_type VARCHAR(255),
    meter_serial_number VARCHAR(255),
    charge_point_model VARCHAR(255),
    iccid VARCHAR(255),
    charge_box_serial_number VARCHAR(255),
    firmware_version VARCHAR(255),
    imsi VARCHAR(255),
    adapter_type VARCHAR(50),
    power_grid_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse table
CREATE TABLE IF NOT EXISTS evse (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_id_seq'),
    charging_station_evse_id INTEGER NOT NULL,
    status VARCHAR(50),
    availability_status VARCHAR(50),
    parking_floor INTEGER,
    parking_row INTEGER,
    parking_column INTEGER,
    parking_place_id VARCHAR(255),
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create connector table
CREATE TABLE IF NOT EXISTS connector (
    id BIGINT PRIMARY KEY DEFAULT nextval('connector_id_seq'),
    evse_connector_id INTEGER NOT NULL,
    status VARCHAR(50),
    availability_status VARCHAR(50),
    last_status_update TIMESTAMP WITH TIME ZONE,
    error_code VARCHAR(255),
    error_info VARCHAR(255),
    vendor_implementation_id VARCHAR(255),
    vendor_error_code VARCHAR(255),
    evse_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse_transaction table
CREATE TABLE IF NOT EXISTS evse_transaction (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_transaction_id_seq'),
    evse_transaction_id VARCHAR(255) NOT NULL,
    evse_id BIGINT NOT NULL,
    connector_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse_transaction_event table
CREATE TABLE IF NOT EXISTS evse_transaction_event (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_transaction_event_id_seq'),
    event_type VARCHAR(50),
    timestamp TIMESTAMP,
    trigger_reason VARCHAR(50),
    event_seq_no INTEGER,
    offline BOOLEAN,
    number_of_phases_used INTEGER,
    cable_max_current INTEGER,
    reservation_id INTEGER,
    charging_state VARCHAR(50),
    time_spent_charging INTEGER,
    stopped_reason VARCHAR(50),
    remote_start_id INTEGER,
    id_token JSONB,
    meter_values JSONB,
    transaction_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station_variable table
CREATE TABLE IF NOT EXISTS charging_station_variable (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_variable_id_seq'),
    component_name VARCHAR(255),
    component_instance VARCHAR(255),
    evse_id INTEGER,
    connector_id INTEGER,
    variable_name VARCHAR(255),
    variable_instance VARCHAR(255),
    unit VARCHAR(255),
    data_type VARCHAR(50),
    min_limit DECIMAL,
    max_limit DECIMAL,
    values_list VARCHAR(255),
    supports_monitoring BOOLEAN,
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station_variable_value table
CREATE TABLE IF NOT EXISTS charging_station_variable_value (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_variable_value_id_seq'),
    type VARCHAR(50),
    value VARCHAR(255),
    mutability VARCHAR(50),
    charging_station_variable_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create report table
CREATE TABLE IF NOT EXISTS report (
    id BIGINT PRIMARY KEY DEFAULT nextval('report_id_seq'),
    request_id BIGINT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50),
    response_status VARCHAR(50),
    response_reason_code VARCHAR(255),
    response_additional_info VARCHAR(255),
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create meter_value table
CREATE TABLE IF NOT EXISTS meter_value (
    id BIGINT PRIMARY KEY DEFAULT nextval('meter_value_id_seq'),
    timestamp TIMESTAMP WITH TIME ZONE,
    value DECIMAL,
    context VARCHAR(50),
    measurand VARCHAR(50),
    phase VARCHAR(50),
    location VARCHAR(50),
    signed_meter_value JSONB,
    unit_of_measure JSONB,
    charging_station_id BIGINT NOT NULL,
    charging_station_evse_id INTEGER,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create csms_user table
CREATE TABLE IF NOT EXISTS csms_user (
    id BIGINT PRIMARY KEY DEFAULT nextval('csms_user_id_seq'),
    user_name VARCHAR(255),
    password VARCHAR(255),
    tenant_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create csms_user_role table
CREATE TABLE IF NOT EXISTS csms_user_role (
    id BIGINT PRIMARY KEY DEFAULT nextval('csms_user_role_id_seq'),
    role VARCHAR(50) NOT NULL,
    user_id BIGINT NOT NULL,
    location_id BIGINT,
    power_grid_id BIGINT,
    charging_station_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create csms_user_tokens table
CREATE TABLE IF NOT EXISTS csms_user_tokens (
    id BIGINT PRIMARY KEY,
    user_name VARCHAR(255) NOT NULL UNIQUE,
    access_token VARCHAR(255) NOT NULL UNIQUE,
    access_token_expires_at TIMESTAMP,
    refresh_token VARCHAR(255),
    refresh_token_expires_at TIMESTAMP,
    auth_server_access_token VARCHAR(2048) NOT NULL,
    auth_server_access_token_expires_at TIMESTAMP NOT NULL,
    auth_server_refresh_token VARCHAR(2048) NOT NULL,
    auth_server_refresh_token_time_skew BIGINT,
    auth_server_id_token VARCHAR(2048) NOT NULL,
    user_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create session_owner table
CREATE TABLE IF NOT EXISTS session_owner (
    id BIGINT PRIMARY KEY DEFAULT nextval('session_owner_id_seq'),
    name VARCHAR(255),
    version INTEGER NOT NULL DEFAULT 0
);

-- Create session table
CREATE TABLE IF NOT EXISTS session (
    id BIGINT PRIMARY KEY DEFAULT nextval('session_id_seq'),
    external_session_id VARCHAR(255),
    start_session_response_url VARCHAR(255),
    start_date_time TIMESTAMP WITH TIME ZONE,
    end_date_time TIMESTAMP WITH TIME ZONE,
    session_token JSONB,
    authorization_reference VARCHAR(255),
    location_id VARCHAR(255),
    evse_uid VARCHAR(255),
    connector_id VARCHAR(255),
    charging_station_session_status VARCHAR(50),
    status VARCHAR(50),
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    session_owner_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create authorization_code_request_data table
CREATE TABLE IF NOT EXISTS authorization_code_request_data (
    id BIGINT PRIMARY KEY DEFAULT nextval('authorization_code_request_data_id_seq'),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    code_verifier VARCHAR(255) NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);