package org.galiasystems.csms.graphql.login.oauth2;

import io.netty.handler.codec.http.HttpResponseStatus;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import jakarta.inject.Inject;
import org.galiasystems.csms.security.CookieService;
import org.galiasystems.csms.test.utils.DatabaseResetUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@QuarkusTest
public class OAuth2ResolverTest {

    @Inject
    CookieService cookieService;

    private static final String GRAPHQL_AUTHORIZATION_CODE_REQUEST_DATA_MUTATION = """
            {
                "query": "mutation { authorizationCodeRequestData { sessionId authorizationEndpoint registrationsEndpoint clientId scope codeChallenge codeChallengeMethod } }"
            }
            """;

    private static final String GRAPHQL_LOGIN_MUTATION = """
            {
                "query": "mutation Login($authorizationCode: String!, $redirectUri: String!) { login(authorizationCode: $authorizationCode, redirectUri: $redirectUri) { userTokens { accessToken accessTokenExpiresAt refreshToken refreshTokenExpiresAt } } }",
                "variables": {
                    "authorizationCode": "%s",
                    "redirectUri": "%s"
                }
            }
            """;

    private static final String GRAPHQL_LOGGED_IN_USER_QUERY = """
            {
                "query": "query { loggedInUser { userTokens { accessToken accessTokenExpiresAt refreshToken refreshTokenExpiresAt } } }"
            }
            """;


    @Test
    @DisplayName("Should return null for logged in user without access token")
    public void testLoggedInUserWithoutAccessToken() {
        given()
                .body(GRAPHQL_LOGGED_IN_USER_QUERY)
                .contentType(ContentType.JSON)
                .post("/graphql/")
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("data.loggedInUser", nullValue());
    }

    @Test
    @DisplayName("Should successfully perform login")
    public void testLoginFlowWithValidSession() {
        try {
            // Step 1: Get authorization code request data
            Response authDataResponse = given()
                    .body(GRAPHQL_AUTHORIZATION_CODE_REQUEST_DATA_MUTATION)
                    .contentType(ContentType.JSON)
                    .post("/graphql/")
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("data.authorizationCodeRequestData.sessionId", notNullValue())
                    .extract().response();

            // Extract session ID from cookie
            var sessionCookie = authDataResponse.getCookie(this.cookieService.getSessionIdCookieName());
            assertNotNull(sessionCookie, "Session cookie should be set");

            // Step 2: Attempt login (MockOidcClient is used)
            String loginQuery = String.format(GRAPHQL_LOGIN_MUTATION, "mock_authorization_code", "mock_redirect_Uri");

            Response loginResponse = given()
                    .cookie(this.cookieService.getSessionIdCookieName(), sessionCookie)
                    .body(loginQuery)
                    .contentType(ContentType.JSON)
                    .post("/graphql/")
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .extract().response();

            var accessTokenCookie = loginResponse.getCookie(this.cookieService.getAccessTokenCookieName());
            assertNotNull(accessTokenCookie, "Access token cookie should be set");

            var responseBody = loginResponse.asString();
            // If the response contains data (successful login), verify token values
            if (responseBody.contains("\"data\"") && !responseBody.contains("\"errors\"")) {
                var accessToken = loginResponse.jsonPath().getString("data.login.userTokens.accessToken");
                var refreshToken = loginResponse.jsonPath().getString("data.login.userTokens.refreshToken");

                assertNotNull(accessToken, "Access token should not be null");
                assertNotNull(refreshToken, "Refresh token should not be null");
            }

            given()
                    .cookie(this.cookieService.getAccessTokenCookieName(), accessTokenCookie)
                    .body(GRAPHQL_LOGGED_IN_USER_QUERY)
                    .contentType(ContentType.JSON)
                    .post("/graphql/")
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("data.loggedInUser", notNullValue());
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

}