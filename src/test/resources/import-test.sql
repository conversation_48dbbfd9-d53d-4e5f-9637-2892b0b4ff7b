-- Test data for H2 database
-- Insert test tenant
INSERT INTO tenant (id, name, version) VALUES (-1, 'Test Tenant', 0);

-- Insert test location
INSERT INTO location (id, name, tenant_id, version) VALUES (-1, 'Test Location', -1, 0);

-- Insert test power grid
INSERT INTO power_grid (id, name, location_id, version) VALUES (-1, 'Test Power Grid', -1, 0);

-- Insert test users
INSERT INTO csms_user (id, user_name, password, tenant_id, version) VALUES (-1, 'admin', '$2a$10$example.hash', -1, 0);
INSERT INTO csms_user (id, user_name, password, tenant_id, version) VALUES (-2, 'user', '$2a$10$example.hash', -1, 0);
INSERT INTO csms_user (id, user_name, password, tenant_id, version) VALUES (-3, 'global-admin', '$2a$10$example.hash', null, 0);

-- Insert test user roles
INSERT INTO csms_user_role (id, role, user_id, location_id, power_grid_id, charging_station_id, version) VALUES (-1, 'GLOBAL_ADMIN', -3, null, null, null, 0);
INSERT INTO csms_user_role (id, role, user_id, location_id, power_grid_id, charging_station_id, version) VALUES (-2, 'LOCATION_ADMIN', -1, -1, null, null, 0);

-- Insert test session owner
INSERT INTO session_owner (id, name, version) VALUES (-1, 'Test Session Owner', 0);
